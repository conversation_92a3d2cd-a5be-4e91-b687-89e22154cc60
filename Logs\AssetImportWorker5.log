Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
D:/nwu/Assembly/UnityProjects/VRAssembly
-logFile
Logs/AssetImportWorker5.log
-srvPort
4081
Successfully changed project path to: D:/nwu/Assembly/UnityProjects/VRAssembly
D:/nwu/Assembly/UnityProjects/VRAssembly
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [40172] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3972364164 [EditorId] 3972364164 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [40172] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3972364164 [EditorId] 3972364164 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 60.30 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 2.22 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/UnityProjects/VRAssembly/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56148
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.015720 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 646 ms
Refreshing native plugins compatible for Editor in 37.83 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.170 seconds
Domain Reload Profiling:
	ReloadAssembly (1170ms)
		BeginReloadAssembly (122ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (948ms)
			LoadAssemblies (109ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (69ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (819ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (699ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (38ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (55ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.014572 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 39.97 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x000002804cb3e1e3 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x000002804cb3debb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x000002804cb3dc40 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x000002804cb3db08 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x000002804cb3af43 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x0000028051b1a9f5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x0000028051b1a09a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x0000028051b19fab (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x0000028051b196f3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x000002804f056298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c8060c43 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.477 seconds
Domain Reload Profiling:
	ReloadAssembly (1477ms)
		BeginReloadAssembly (146ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (12ms)
		EndReloadAssembly (1224ms)
			LoadAssemblies (133ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (191ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (40ms)
			SetupLoadedEditorAssemblies (880ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (40ms)
				BeforeProcessingInitializeOnLoad (67ms)
				ProcessInitializeOnLoadAttributes (706ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (27ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.21 ms.
Unloading 4794 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (122.3 KB). Loaded Objects now: 5248.
Memory consumption went from 195.7 MB to 195.6 MB.
Total: 2.702400 ms (FindLiveObjects: 0.212400 ms CreateObjectMapping: 0.070000 ms MarkObjects: 2.349000 ms  DeleteObjects: 0.070400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
