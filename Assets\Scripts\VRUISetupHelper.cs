using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// VR UI设置助手
/// 
/// 提供快速设置VR UI系统的工具方法
/// 可以通过代码或Unity编辑器菜单快速配置VR UI
/// </summary>
public class VRUISetupHelper : MonoBehaviour
{
    [Header("自动设置选项")]
    [SerializeField] private bool setupOnStart = false;
    [SerializeField] private bool createSampleUI = true;
    [SerializeField] private bool optimizeForVR = true;
    [SerializeField] private bool enableDebugMode = false;
    
    [Header("UI配置")]
    [SerializeField] private float uiDistance = 2.5f;
    [SerializeField] private float uiScale = 0.012f;
    [SerializeField] private Vector3 uiPosition = new Vector3(0, 1.5f, 2.5f);
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupVRUI();
        }
    }
    
    /// <summary>
    /// 设置VR UI系统
    /// </summary>
    [ContextMenu("设置VR UI系统")]
    public void SetupVRUI()
    {
        Debug.Log("[VRUISetupHelper] 开始设置VR UI系统");
        
        // 1. 查找或创建Canvas
        Canvas canvas = SetupCanvas();
        
        // 2. 配置事件系统
        SetupEventSystem();
        
        // 3. 添加VR UI组件
        SetupVRUIComponents(canvas);
        
        // 4. 创建示例UI（如果需要）
        if (createSampleUI)
        {
            CreateSampleUI(canvas);
        }
        
        // 5. 优化VR设置
        if (optimizeForVR)
        {
            OptimizeForVR(canvas);
        }
        
        Debug.Log("[VRUISetupHelper] VR UI系统设置完成");
    }
    
    /// <summary>
    /// 设置Canvas
    /// </summary>
    /// <returns>配置好的Canvas</returns>
    private Canvas SetupCanvas()
    {
        Canvas canvas = FindObjectOfType<Canvas>();
        
        if (canvas == null)
        {
            // 创建新的Canvas
            GameObject canvasGO = new GameObject("VR Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }
        
        // 配置Canvas为VR模式
        canvas.renderMode = RenderMode.WorldSpace;
        
        // 查找VR相机
        Camera vrCamera = Camera.main;
        if (vrCamera == null)
        {
            vrCamera = FindObjectOfType<Camera>();
        }
        
        if (vrCamera != null)
        {
            canvas.worldCamera = vrCamera;
        }
        
        // 设置Canvas位置和缩放
        canvas.transform.position = uiPosition;
        canvas.transform.localScale = Vector3.one * uiScale;
        
        // 让Canvas面向相机
        if (vrCamera != null)
        {
            canvas.transform.LookAt(vrCamera.transform);
            canvas.transform.Rotate(0, 180, 0);
        }
        
        // 配置CanvasScaler
        CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
        if (scaler != null)
        {
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;
        }
        
#if UNITY_XR_INTERACTION_TOOLKIT
        // 添加TrackedDeviceGraphicRaycaster
        var trackedRaycaster = canvas.GetComponent<UnityEngine.XR.Interaction.Toolkit.UI.TrackedDeviceGraphicRaycaster>();
        if (trackedRaycaster == null)
        {
            canvas.gameObject.AddComponent<UnityEngine.XR.Interaction.Toolkit.UI.TrackedDeviceGraphicRaycaster>();
        }
#endif
        
        Debug.Log("[VRUISetupHelper] Canvas配置完成");
        return canvas;
    }
    
    /// <summary>
    /// 设置事件系统
    /// </summary>
    private void SetupEventSystem()
    {
        EventSystem eventSystem = FindObjectOfType<EventSystem>();
        
        if (eventSystem == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystem = eventSystemGO.AddComponent<EventSystem>();
            
#if UNITY_XR_INTERACTION_TOOLKIT
            // 添加XR UI输入模块
            eventSystemGO.AddComponent<UnityEngine.XR.Interaction.Toolkit.UI.XRUIInputModule>();
#else
            // 添加标准输入模块
            eventSystemGO.AddComponent<StandaloneInputModule>();
#endif
        }
        
        Debug.Log("[VRUISetupHelper] 事件系统配置完成");
    }
    
    /// <summary>
    /// 设置VR UI组件
    /// </summary>
    /// <param name="canvas">Canvas对象</param>
    private void SetupVRUIComponents(Canvas canvas)
    {
        // 添加VR UI管理器
        VRUIManager vrUIManager = canvas.GetComponent<VRUIManager>();
        if (vrUIManager == null)
        {
            vrUIManager = canvas.gameObject.AddComponent<VRUIManager>();
        }
        
        // 添加VR UI交互器
        VRUIInteractor vrUIInteractor = canvas.GetComponent<VRUIInteractor>();
        if (vrUIInteractor == null)
        {
            vrUIInteractor = canvas.gameObject.AddComponent<VRUIInteractor>();
        }
        
        // 添加VR UI布局优化器
        VRUILayoutOptimizer vrUIOptimizer = canvas.GetComponent<VRUILayoutOptimizer>();
        if (vrUIOptimizer == null)
        {
            vrUIOptimizer = canvas.gameObject.AddComponent<VRUILayoutOptimizer>();
        }
        
        // 添加VR装配UI集成器
        VRAssemblyUIIntegrator vrUIIntegrator = canvas.GetComponent<VRAssemblyUIIntegrator>();
        if (vrUIIntegrator == null)
        {
            vrUIIntegrator = canvas.gameObject.AddComponent<VRAssemblyUIIntegrator>();
        }
        
        Debug.Log("[VRUISetupHelper] VR UI组件添加完成");
    }
    
    /// <summary>
    /// 创建示例UI
    /// </summary>
    /// <param name="canvas">Canvas对象</param>
    private void CreateSampleUI(Canvas canvas)
    {
        // 检查是否已存在UI元素
        if (canvas.transform.childCount > 0)
        {
            Debug.Log("[VRUISetupHelper] Canvas已有UI元素，跳过示例UI创建");
            return;
        }
        
        // 创建主面板
        GameObject mainPanel = CreateUIPanel(canvas.transform, "MainPanel", new Vector2(800, 600));
        
        // 创建标题
        CreateUIText(mainPanel.transform, "Title", "VR装配控制面板", new Vector2(0, 200), 36);
        
        // 创建状态文本
        CreateUIText(mainPanel.transform, "StatusText", "状态: 准备就绪", new Vector2(0, 150), 24);
        
        // 创建选中零件文本
        CreateUIText(mainPanel.transform, "SelectedPartText", "选中零件: 无", new Vector2(0, 100), 24);
        
        // 创建步骤计数文本
        CreateUIText(mainPanel.transform, "StepsCountText", "步骤: 0/0", new Vector2(0, 50), 24);
        
        // 创建控制按钮
        CreateUIButton(mainPanel.transform, "NextStepButton", "下一步", new Vector2(-150, -50), new Vector2(120, 60));
        CreateUIButton(mainPanel.transform, "ResetButton", "重置", new Vector2(0, -50), new Vector2(120, 60));
        CreateUIButton(mainPanel.transform, "ReplayButton", "重播", new Vector2(150, -50), new Vector2(120, 60));
        
        // 创建自动播放开关
        CreateUIToggle(mainPanel.transform, "AutoPlayToggle", "自动播放", new Vector2(-100, -150));
        
        // 创建速度滑动条
        GameObject speedSlider = CreateUISlider(mainPanel.transform, "SpeedSlider", new Vector2(100, -150), new Vector2(200, 20));
        CreateUIText(mainPanel.transform, "SpeedValueText", "速率: 1.0x", new Vector2(100, -200), 18);
        
        // 创建VR控制面板
        GameObject vrControlPanel = CreateUIPanel(canvas.transform, "VRControlPanel", new Vector2(400, 300));
        vrControlPanel.transform.localPosition = new Vector3(500, 0, 0);
        
        CreateUIText(vrControlPanel.transform, "VRTitle", "VR控制", new Vector2(0, 100), 28);
        CreateUIButton(vrControlPanel.transform, "VRRepositionButton", "重新定位", new Vector2(0, 50), new Vector2(150, 50));
        CreateUIButton(vrControlPanel.transform, "VRResetViewButton", "重置视图", new Vector2(0, 0), new Vector2(150, 50));
        CreateUIToggle(vrControlPanel.transform, "VRFollowModeToggle", "跟随模式", new Vector2(0, -50));
        
        Debug.Log("[VRUISetupHelper] 示例UI创建完成");
    }
    
    /// <summary>
    /// 创建UI面板
    /// </summary>
    private GameObject CreateUIPanel(Transform parent, string name, Vector2 size)
    {
        GameObject panel = new GameObject(name);
        panel.transform.SetParent(parent, false);
        
        RectTransform rectTransform = panel.AddComponent<RectTransform>();
        rectTransform.sizeDelta = size;
        
        Image image = panel.AddComponent<Image>();
        image.color = new Color(0.1f, 0.1f, 0.1f, 0.8f);
        
        return panel;
    }
    
    /// <summary>
    /// 创建UI文本
    /// </summary>
    private GameObject CreateUIText(Transform parent, string name, string text, Vector2 position, int fontSize)
    {
        GameObject textGO = new GameObject(name);
        textGO.transform.SetParent(parent, false);
        
        RectTransform rectTransform = textGO.AddComponent<RectTransform>();
        rectTransform.anchoredPosition = position;
        rectTransform.sizeDelta = new Vector2(300, 50);
        
        Text textComponent = textGO.AddComponent<Text>();
        textComponent.text = text;
        textComponent.fontSize = fontSize;
        textComponent.color = Color.white;
        textComponent.alignment = TextAnchor.MiddleCenter;
        textComponent.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        
        return textGO;
    }
    
    /// <summary>
    /// 创建UI按钮
    /// </summary>
    private GameObject CreateUIButton(Transform parent, string name, string text, Vector2 position, Vector2 size)
    {
        GameObject buttonGO = new GameObject(name);
        buttonGO.transform.SetParent(parent, false);
        
        RectTransform rectTransform = buttonGO.AddComponent<RectTransform>();
        rectTransform.anchoredPosition = position;
        rectTransform.sizeDelta = size;
        
        Image image = buttonGO.AddComponent<Image>();
        image.color = new Color(0.2f, 0.4f, 0.8f, 1f);
        
        Button button = buttonGO.AddComponent<Button>();
        button.targetGraphic = image;
        
        // 创建按钮文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        
        RectTransform textRect = textGO.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;
        textRect.anchoredPosition = Vector2.zero;
        
        Text textComponent = textGO.AddComponent<Text>();
        textComponent.text = text;
        textComponent.fontSize = 18;
        textComponent.color = Color.white;
        textComponent.alignment = TextAnchor.MiddleCenter;
        textComponent.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        
        return buttonGO;
    }
    
    /// <summary>
    /// 创建UI开关
    /// </summary>
    private GameObject CreateUIToggle(Transform parent, string name, string text, Vector2 position)
    {
        GameObject toggleGO = new GameObject(name);
        toggleGO.transform.SetParent(parent, false);
        
        RectTransform rectTransform = toggleGO.AddComponent<RectTransform>();
        rectTransform.anchoredPosition = position;
        rectTransform.sizeDelta = new Vector2(150, 30);
        
        Toggle toggle = toggleGO.AddComponent<Toggle>();
        
        // 创建背景
        GameObject background = new GameObject("Background");
        background.transform.SetParent(toggleGO.transform, false);
        
        RectTransform bgRect = background.AddComponent<RectTransform>();
        bgRect.anchorMin = new Vector2(0, 0.5f);
        bgRect.anchorMax = new Vector2(0, 0.5f);
        bgRect.anchoredPosition = new Vector2(10, 0);
        bgRect.sizeDelta = new Vector2(20, 20);
        
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = new Color(0.3f, 0.3f, 0.3f, 1f);
        
        // 创建勾选标记
        GameObject checkmark = new GameObject("Checkmark");
        checkmark.transform.SetParent(background.transform, false);
        
        RectTransform checkRect = checkmark.AddComponent<RectTransform>();
        checkRect.anchorMin = Vector2.zero;
        checkRect.anchorMax = Vector2.one;
        checkRect.sizeDelta = Vector2.zero;
        checkRect.anchoredPosition = Vector2.zero;
        
        Image checkImage = checkmark.AddComponent<Image>();
        checkImage.color = Color.green;
        
        // 创建标签文本
        GameObject labelGO = new GameObject("Label");
        labelGO.transform.SetParent(toggleGO.transform, false);
        
        RectTransform labelRect = labelGO.AddComponent<RectTransform>();
        labelRect.anchorMin = new Vector2(0.3f, 0);
        labelRect.anchorMax = new Vector2(1, 1);
        labelRect.sizeDelta = Vector2.zero;
        labelRect.anchoredPosition = Vector2.zero;
        
        Text labelText = labelGO.AddComponent<Text>();
        labelText.text = text;
        labelText.fontSize = 16;
        labelText.color = Color.white;
        labelText.alignment = TextAnchor.MiddleLeft;
        labelText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        
        // 配置Toggle
        toggle.targetGraphic = bgImage;
        toggle.graphic = checkImage;
        
        return toggleGO;
    }
    
    /// <summary>
    /// 创建UI滑动条
    /// </summary>
    private GameObject CreateUISlider(Transform parent, string name, Vector2 position, Vector2 size)
    {
        GameObject sliderGO = new GameObject(name);
        sliderGO.transform.SetParent(parent, false);
        
        RectTransform rectTransform = sliderGO.AddComponent<RectTransform>();
        rectTransform.anchoredPosition = position;
        rectTransform.sizeDelta = size;
        
        Slider slider = sliderGO.AddComponent<Slider>();
        
        // 创建背景
        GameObject background = new GameObject("Background");
        background.transform.SetParent(sliderGO.transform, false);
        
        RectTransform bgRect = background.AddComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.sizeDelta = Vector2.zero;
        bgRect.anchoredPosition = Vector2.zero;
        
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = new Color(0.3f, 0.3f, 0.3f, 1f);
        
        // 创建填充区域
        GameObject fillArea = new GameObject("Fill Area");
        fillArea.transform.SetParent(sliderGO.transform, false);
        
        RectTransform fillAreaRect = fillArea.AddComponent<RectTransform>();
        fillAreaRect.anchorMin = Vector2.zero;
        fillAreaRect.anchorMax = Vector2.one;
        fillAreaRect.sizeDelta = Vector2.zero;
        fillAreaRect.anchoredPosition = Vector2.zero;
        
        GameObject fill = new GameObject("Fill");
        fill.transform.SetParent(fillArea.transform, false);
        
        RectTransform fillRect = fill.AddComponent<RectTransform>();
        fillRect.anchorMin = Vector2.zero;
        fillRect.anchorMax = Vector2.one;
        fillRect.sizeDelta = Vector2.zero;
        fillRect.anchoredPosition = Vector2.zero;
        
        Image fillImage = fill.AddComponent<Image>();
        fillImage.color = new Color(0.2f, 0.4f, 0.8f, 1f);
        
        // 创建手柄
        GameObject handleSlideArea = new GameObject("Handle Slide Area");
        handleSlideArea.transform.SetParent(sliderGO.transform, false);
        
        RectTransform handleAreaRect = handleSlideArea.AddComponent<RectTransform>();
        handleAreaRect.anchorMin = Vector2.zero;
        handleAreaRect.anchorMax = Vector2.one;
        handleAreaRect.sizeDelta = Vector2.zero;
        handleAreaRect.anchoredPosition = Vector2.zero;
        
        GameObject handle = new GameObject("Handle");
        handle.transform.SetParent(handleSlideArea.transform, false);
        
        RectTransform handleRect = handle.AddComponent<RectTransform>();
        handleRect.sizeDelta = new Vector2(20, 20);
        
        Image handleImage = handle.AddComponent<Image>();
        handleImage.color = Color.white;
        
        // 配置Slider
        slider.targetGraphic = handleImage;
        slider.fillRect = fillRect;
        slider.handleRect = handleRect;
        slider.minValue = 0.1f;
        slider.maxValue = 5.0f;
        slider.value = 1.0f;
        
        return sliderGO;
    }
    
    /// <summary>
    /// 优化VR设置
    /// </summary>
    /// <param name="canvas">Canvas对象</param>
    private void OptimizeForVR(Canvas canvas)
    {
        VRUILayoutOptimizer optimizer = canvas.GetComponent<VRUILayoutOptimizer>();
        if (optimizer != null)
        {
            optimizer.OptimizeAllUIElements();
        }
        
        VRAssemblyUIIntegrator integrator = canvas.GetComponent<VRAssemblyUIIntegrator>();
        if (integrator != null)
        {
            integrator.SetVRMode(true);
            integrator.SetDebugMode(enableDebugMode);
        }
        
        Debug.Log("[VRUISetupHelper] VR优化完成");
    }
    
#if UNITY_EDITOR
    /// <summary>
    /// 编辑器菜单：快速设置VR UI
    /// </summary>
    [MenuItem("VR Assembly/Setup VR UI System")]
    public static void SetupVRUIFromMenu()
    {
        // 查找或创建VRUISetupHelper
        VRUISetupHelper helper = FindObjectOfType<VRUISetupHelper>();
        if (helper == null)
        {
            GameObject helperGO = new GameObject("VRUISetupHelper");
            helper = helperGO.AddComponent<VRUISetupHelper>();
        }
        
        helper.SetupVRUI();
        
        EditorUtility.DisplayDialog("VR UI设置", "VR UI系统设置完成！", "确定");
    }
    
    /// <summary>
    /// 编辑器菜单：创建示例VR UI
    /// </summary>
    [MenuItem("VR Assembly/Create Sample VR UI")]
    public static void CreateSampleVRUIFromMenu()
    {
        VRUISetupHelper helper = FindObjectOfType<VRUISetupHelper>();
        if (helper == null)
        {
            GameObject helperGO = new GameObject("VRUISetupHelper");
            helper = helperGO.AddComponent<VRUISetupHelper>();
        }
        
        helper.createSampleUI = true;
        helper.SetupVRUI();
        
        EditorUtility.DisplayDialog("示例UI创建", "示例VR UI创建完成！", "确定");
    }
#endif
}
