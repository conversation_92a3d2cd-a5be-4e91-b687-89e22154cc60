# VR UI系统使用指南

## 概述

本指南介绍如何在PICO VR环境中使用新的VR UI系统。该系统专门为VR环境设计，提供了完整的装配动画控制界面，支持VR控制器交互、自动布局优化和视觉效果增强。

## 系统组件

### 核心组件

1. **Neo4jAssemblyUI** - 增强版装配UI，支持VR模式
2. **VRUIManager** - VR UI管理器，处理Canvas配置和定位
3. **VRUIInteractor** - VR UI交互器，处理控制器与UI的交互
4. **VRUILayoutOptimizer** - VR UI布局优化器，自动优化VR环境下的UI
5. **VRAssemblyUIIntegrator** - VR装配UI集成器，整合所有VR UI功能

### 支持组件

- **VRInputManager** - VR输入管理器（已存在）
- **EventSystem** - Unity事件系统
- **TrackedDeviceGraphicRaycaster** - VR UI射线检测

## 快速开始

### 1. 基础设置

在您的装配场景中添加VR UI系统：

```csharp
// 在现有的Canvas上添加VRAssemblyUIIntegrator组件
GameObject canvasGO = GameObject.Find("Canvas");
VRAssemblyUIIntegrator integrator = canvasGO.AddComponent<VRAssemblyUIIntegrator>();
```

### 2. 自动配置

系统支持自动配置，启动时会：
- 自动检测VR环境
- 配置Canvas为World Space模式
- 添加必要的VR交互组件
- 优化UI布局和视觉效果

### 3. 手动配置

如果需要手动配置，请按以下步骤：

#### 步骤1：配置Canvas
```csharp
Canvas canvas = GetComponent<Canvas>();
canvas.renderMode = RenderMode.WorldSpace;
canvas.worldCamera = Camera.main; // VR相机
```

#### 步骤2：添加VR交互组件
```csharp
// 添加TrackedDeviceGraphicRaycaster
canvas.gameObject.AddComponent<TrackedDeviceGraphicRaycaster>();

// 添加VR UI管理器
VRUIManager vrUIManager = canvas.gameObject.AddComponent<VRUIManager>();

// 添加VR UI交互器
VRUIInteractor vrUIInteractor = canvas.gameObject.AddComponent<VRUIInteractor>();
```

#### 步骤3：优化UI布局
```csharp
// 添加布局优化器
VRUILayoutOptimizer optimizer = canvas.gameObject.AddComponent<VRUILayoutOptimizer>();
optimizer.OptimizeAllUIElements();
```

## 功能特性

### 1. VR控制器交互

- **射线交互**: 使用控制器射线指向UI元素
- **触觉反馈**: 悬停和点击时提供触觉反馈
- **音效反馈**: 支持悬停和点击音效
- **视觉反馈**: 悬停时高亮显示，点击时缩放效果

### 2. 自动布局优化

- **文本优化**: 自动调整字体大小和样式，提高可读性
- **按钮优化**: 调整按钮大小，适合VR交互
- **间距优化**: 增加UI元素间距，避免误操作
- **颜色优化**: 增强对比度，改善VR环境下的视觉效果

### 3. UI定位系统

- **自动定位**: UI自动出现在用户前方
- **跟随模式**: UI可以跟随用户头部移动
- **面向用户**: UI始终面向用户，保持最佳观看角度
- **距离调节**: 可调整UI与用户的距离

### 4. 装配控制集成

- **零件选择**: 通过VR控制器选择装配零件
- **步骤控制**: 使用VR控制器控制装配步骤
- **状态显示**: 实时显示装配状态和进度
- **速度控制**: VR环境下的动画速度调节

## 配置参数

### VRUIManager 配置

```csharp
[Header("UI定位设置")]
public float uiDistance = 2.0f;        // UI距离用户的距离
public float uiScale = 0.01f;          // UI缩放比例
public Vector3 uiOffset = Vector3.zero; // UI偏移
public bool followUserHead = false;     // 是否跟随用户头部
public bool alwaysFaceUser = true;      // 是否始终面向用户
```

### VRUILayoutOptimizer 配置

```csharp
[Header("文本优化")]
public float minTextSize = 24f;         // 最小文本大小
public float maxTextSize = 72f;         // 最大文本大小
public float textSizeMultiplier = 1.5f; // 文本大小倍数

[Header("按钮优化")]
public Vector2 minButtonSize = new Vector2(120f, 60f); // 最小按钮大小
public Vector2 maxButtonSize = new Vector2(300f, 120f); // 最大按钮大小
public float buttonSizeMultiplier = 1.3f; // 按钮大小倍数
```

### VRUIInteractor 配置

```csharp
[Header("视觉反馈")]
public Color hoverColor = Color.yellow;     // 悬停颜色
public Color clickColor = Color.green;      // 点击颜色
public float hoverScaleMultiplier = 1.05f;  // 悬停缩放倍数

[Header("触觉反馈")]
public float hoverHapticIntensity = 0.1f;   // 悬停触觉强度
public float clickHapticIntensity = 0.3f;   // 点击触觉强度
public float hapticDuration = 0.1f;         // 触觉持续时间
```

## 使用示例

### 1. 基本使用

```csharp
// 获取VR UI集成器
VRAssemblyUIIntegrator integrator = FindObjectOfType<VRAssemblyUIIntegrator>();

// 启用VR模式
integrator.SetVRMode(true);

// 启用调试模式
integrator.SetDebugMode(true);
```

### 2. 动态调整UI

```csharp
// 获取VR UI管理器
VRUIManager vrUIManager = FindObjectOfType<VRUIManager>();

// 调整UI距离
vrUIManager.SetUIDistance(3.0f);

// 调整UI缩放
vrUIManager.SetUIScale(0.015f);

// 重新定位UI到用户前方
vrUIManager.RepositionUIToUser();
```

### 3. 自定义交互

```csharp
// 获取VR UI交互器
VRUIInteractor vrUIInteractor = FindObjectOfType<VRUIInteractor>();

// 可以通过代码自定义交互行为
// 例如添加自定义的悬停效果或点击处理
```

## 最佳实践

### 1. UI设计原则

- **大字体**: 使用至少24pt的字体大小
- **高对比度**: 使用高对比度的颜色组合
- **大按钮**: 按钮至少120x60像素
- **充足间距**: UI元素间保持足够间距

### 2. 交互设计

- **清晰反馈**: 提供明确的视觉和触觉反馈
- **容错设计**: 避免误操作，提供撤销功能
- **简化操作**: 减少复杂的手势，优先使用简单点击

### 3. 性能优化

- **合理缩放**: 避免过大的UI元素影响性能
- **减少动画**: 限制同时播放的UI动画数量
- **优化材质**: 使用简单的UI材质和着色器

## 故障排除

### 常见问题

1. **UI不显示**
   - 检查Canvas是否设置为World Space模式
   - 确认VR相机引用正确
   - 验证UI距离和缩放设置

2. **控制器无法交互**
   - 确认XR Interaction Toolkit已安装
   - 检查TrackedDeviceGraphicRaycaster组件
   - 验证控制器射线配置

3. **UI布局异常**
   - 运行VRUILayoutOptimizer.OptimizeAllUIElements()
   - 检查UI元素的RectTransform设置
   - 确认Canvas Scaler配置

### 调试工具

启用调试模式可以查看：
- VR系统状态
- 组件连接状态
- 控制器状态
- 实时性能信息

```csharp
// 启用调试模式
VRAssemblyUIIntegrator integrator = FindObjectOfType<VRAssemblyUIIntegrator>();
integrator.SetDebugMode(true);
```

## 扩展开发

### 自定义UI元素

可以通过继承现有组件来创建自定义的VR UI元素：

```csharp
public class CustomVRButton : MonoBehaviour
{
    private VRUIInteractor interactor;
    
    void Start()
    {
        interactor = FindObjectOfType<VRUIInteractor>();
        // 添加自定义交互逻辑
    }
}
```

### 添加新的交互方式

可以扩展VRUIInteractor来支持新的交互方式：

```csharp
public class ExtendedVRUIInteractor : VRUIInteractor
{
    // 添加手势识别
    // 添加语音控制
    // 添加眼动追踪
}
```

## 总结

VR UI系统为PICO VR环境提供了完整的装配动画控制界面解决方案。通过自动优化和智能交互，确保用户在VR环境中获得最佳的操作体验。系统具有良好的扩展性，可以根据具体需求进行定制和扩展。
