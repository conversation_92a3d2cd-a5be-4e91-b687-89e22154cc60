using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// VR UI布局优化器
/// 
/// 自动优化UI元素在VR环境中的布局、大小和可读性
/// 确保在头显中有良好的用户体验
/// </summary>
public class VRUILayoutOptimizer : MonoBehaviour
{
    [Header("布局设置")]
    [SerializeField] private bool autoOptimizeOnStart = true;
    [SerializeField] private bool optimizeTextSize = true;
    [SerializeField] private bool optimizeButtonSize = true;
    [SerializeField] private bool optimizeSpacing = true;
    [SerializeField] private bool optimizeColors = true;
    
    [Header("文本优化")]
    [SerializeField] private float minTextSize = 24f;
    [SerializeField] private float maxTextSize = 72f;
    [SerializeField] private float textSizeMultiplier = 1.5f;
    [SerializeField] private FontStyle vrFontStyle = FontStyle.Bold;
    
    [Header("按钮优化")]
    [SerializeField] private Vector2 minButtonSize = new Vector2(120f, 60f);
    [SerializeField] private Vector2 maxButtonSize = new Vector2(300f, 120f);
    [SerializeField] private float buttonSizeMultiplier = 1.3f;
    
    [Header("间距优化")]
    [SerializeField] private float spacingMultiplier = 1.5f;
    [SerializeField] private float minSpacing = 20f;
    [SerializeField] private float maxSpacing = 100f;
    
    [Header("颜色优化")]
    [SerializeField] private float contrastBoost = 0.2f;
    [SerializeField] private Color vrBackgroundColor = new Color(0.1f, 0.1f, 0.1f, 0.9f);
    [SerializeField] private Color vrTextColor = Color.white;
    [SerializeField] private Color vrButtonColor = new Color(0.2f, 0.4f, 0.8f, 1f);
    
    [Header("特殊元素")]
    [SerializeField] private bool optimizeSliders = true;
    [SerializeField] private float sliderHandleSize = 30f;
    [SerializeField] private float sliderThickness = 20f;
    
    // 优化记录
    private List<OptimizationRecord> optimizationRecords = new List<OptimizationRecord>();
    
    /// <summary>
    /// 优化记录
    /// </summary>
    private class OptimizationRecord
    {
        public GameObject target;
        public string componentType;
        public Dictionary<string, object> originalValues;
        public Dictionary<string, object> optimizedValues;
        
        public OptimizationRecord(GameObject obj, string type)
        {
            target = obj;
            componentType = type;
            originalValues = new Dictionary<string, object>();
            optimizedValues = new Dictionary<string, object>();
        }
    }
    
    void Start()
    {
        if (autoOptimizeOnStart)
        {
            OptimizeAllUIElements();
        }
    }
    
    /// <summary>
    /// 优化所有UI元素
    /// </summary>
    public void OptimizeAllUIElements()
    {
        Debug.Log("[VRUILayoutOptimizer] 开始优化UI布局");
        
        // 清除之前的记录
        optimizationRecords.Clear();
        
        // 获取Canvas下的所有UI元素
        Canvas canvas = GetComponent<Canvas>();
        if (canvas == null)
        {
            canvas = GetComponentInParent<Canvas>();
        }
        
        if (canvas == null)
        {
            Debug.LogWarning("[VRUILayoutOptimizer] 未找到Canvas，无法进行优化");
            return;
        }
        
        // 优化不同类型的UI元素
        OptimizeTexts(canvas.transform);
        OptimizeButtons(canvas.transform);
        OptimizeSliders(canvas.transform);
        OptimizeImages(canvas.transform);
        OptimizeLayoutGroups(canvas.transform);
        
        Debug.Log($"[VRUILayoutOptimizer] 优化完成，共优化了 {optimizationRecords.Count} 个元素");
    }
    
    /// <summary>
    /// 优化文本元素
    /// </summary>
    /// <param name="parent">父级Transform</param>
    private void OptimizeTexts(Transform parent)
    {
        if (!optimizeTextSize) return;
        
        Text[] texts = parent.GetComponentsInChildren<Text>();
        foreach (var text in texts)
        {
            OptimizeText(text);
        }
    }
    
    /// <summary>
    /// 优化单个文本元素
    /// </summary>
    /// <param name="text">文本组件</param>
    private void OptimizeText(Text text)
    {
        var record = new OptimizationRecord(text.gameObject, "Text");
        
        // 记录原始值
        record.originalValues["fontSize"] = text.fontSize;
        record.originalValues["fontStyle"] = text.fontStyle;
        record.originalValues["color"] = text.color;
        
        // 优化字体大小
        float newFontSize = Mathf.Clamp(text.fontSize * textSizeMultiplier, minTextSize, maxTextSize);
        text.fontSize = Mathf.RoundToInt(newFontSize);
        
        // 优化字体样式
        text.fontStyle = vrFontStyle;
        
        // 优化颜色对比度
        if (optimizeColors)
        {
            text.color = OptimizeTextColor(text.color);
        }
        
        // 记录优化后的值
        record.optimizedValues["fontSize"] = text.fontSize;
        record.optimizedValues["fontStyle"] = text.fontStyle;
        record.optimizedValues["color"] = text.color;
        
        optimizationRecords.Add(record);
    }
    
    /// <summary>
    /// 优化按钮元素
    /// </summary>
    /// <param name="parent">父级Transform</param>
    private void OptimizeButtons(Transform parent)
    {
        if (!optimizeButtonSize) return;
        
        Button[] buttons = parent.GetComponentsInChildren<Button>();
        foreach (var button in buttons)
        {
            OptimizeButton(button);
        }
    }
    
    /// <summary>
    /// 优化单个按钮元素
    /// </summary>
    /// <param name="button">按钮组件</param>
    private void OptimizeButton(Button button)
    {
        var record = new OptimizationRecord(button.gameObject, "Button");
        RectTransform rectTransform = button.GetComponent<RectTransform>();
        
        if (rectTransform != null)
        {
            // 记录原始值
            record.originalValues["sizeDelta"] = rectTransform.sizeDelta;
            
            // 优化按钮大小
            Vector2 currentSize = rectTransform.sizeDelta;
            Vector2 newSize = new Vector2(
                Mathf.Clamp(currentSize.x * buttonSizeMultiplier, minButtonSize.x, maxButtonSize.x),
                Mathf.Clamp(currentSize.y * buttonSizeMultiplier, minButtonSize.y, maxButtonSize.y)
            );
            rectTransform.sizeDelta = newSize;
            
            record.optimizedValues["sizeDelta"] = newSize;
        }
        
        // 优化按钮颜色
        if (optimizeColors)
        {
            Image buttonImage = button.GetComponent<Image>();
            if (buttonImage != null)
            {
                record.originalValues["color"] = buttonImage.color;
                buttonImage.color = vrButtonColor;
                record.optimizedValues["color"] = buttonImage.color;
            }
        }
        
        // 优化按钮文本
        Text buttonText = button.GetComponentInChildren<Text>();
        if (buttonText != null)
        {
            OptimizeText(buttonText);
        }
        
        optimizationRecords.Add(record);
    }
    
    /// <summary>
    /// 优化滑动条元素
    /// </summary>
    /// <param name="parent">父级Transform</param>
    private void OptimizeSliders(Transform parent)
    {
        if (!optimizeSliders) return;
        
        Slider[] sliders = parent.GetComponentsInChildren<Slider>();
        foreach (var slider in sliders)
        {
            OptimizeSlider(slider);
        }
    }
    
    /// <summary>
    /// 优化单个滑动条元素
    /// </summary>
    /// <param name="slider">滑动条组件</param>
    private void OptimizeSlider(Slider slider)
    {
        var record = new OptimizationRecord(slider.gameObject, "Slider");
        
        // 优化滑动条手柄大小
        if (slider.handleRect != null)
        {
            record.originalValues["handleSize"] = slider.handleRect.sizeDelta;
            slider.handleRect.sizeDelta = Vector2.one * sliderHandleSize;
            record.optimizedValues["handleSize"] = slider.handleRect.sizeDelta;
        }
        
        // 优化滑动条背景厚度
        RectTransform sliderRect = slider.GetComponent<RectTransform>();
        if (sliderRect != null)
        {
            record.originalValues["height"] = sliderRect.sizeDelta.y;
            Vector2 newSize = sliderRect.sizeDelta;
            newSize.y = sliderThickness;
            sliderRect.sizeDelta = newSize;
            record.optimizedValues["height"] = newSize.y;
        }
        
        optimizationRecords.Add(record);
    }
    
    /// <summary>
    /// 优化图像元素
    /// </summary>
    /// <param name="parent">父级Transform</param>
    private void OptimizeImages(Transform parent)
    {
        if (!optimizeColors) return;
        
        Image[] images = parent.GetComponentsInChildren<Image>();
        foreach (var image in images)
        {
            // 跳过按钮的图像（已在按钮优化中处理）
            if (image.GetComponent<Button>() != null) continue;
            
            OptimizeImage(image);
        }
    }
    
    /// <summary>
    /// 优化单个图像元素
    /// </summary>
    /// <param name="image">图像组件</param>
    private void OptimizeImage(Image image)
    {
        var record = new OptimizationRecord(image.gameObject, "Image");
        
        // 记录原始颜色
        record.originalValues["color"] = image.color;
        
        // 如果是背景图像，应用VR背景颜色
        if (image.name.ToLower().Contains("background") || image.name.ToLower().Contains("panel"))
        {
            image.color = vrBackgroundColor;
        }
        else
        {
            // 增强对比度
            image.color = OptimizeImageColor(image.color);
        }
        
        record.optimizedValues["color"] = image.color;
        optimizationRecords.Add(record);
    }
    
    /// <summary>
    /// 优化布局组
    /// </summary>
    /// <param name="parent">父级Transform</param>
    private void OptimizeLayoutGroups(Transform parent)
    {
        if (!optimizeSpacing) return;
        
        // 优化水平布局组
        HorizontalLayoutGroup[] hLayoutGroups = parent.GetComponentsInChildren<HorizontalLayoutGroup>();
        foreach (var layoutGroup in hLayoutGroups)
        {
            OptimizeHorizontalLayoutGroup(layoutGroup);
        }
        
        // 优化垂直布局组
        VerticalLayoutGroup[] vLayoutGroups = parent.GetComponentsInChildren<VerticalLayoutGroup>();
        foreach (var layoutGroup in vLayoutGroups)
        {
            OptimizeVerticalLayoutGroup(layoutGroup);
        }
        
        // 优化网格布局组
        GridLayoutGroup[] gridLayoutGroups = parent.GetComponentsInChildren<GridLayoutGroup>();
        foreach (var layoutGroup in gridLayoutGroups)
        {
            OptimizeGridLayoutGroup(layoutGroup);
        }
    }
    
    /// <summary>
    /// 优化水平布局组
    /// </summary>
    /// <param name="layoutGroup">水平布局组</param>
    private void OptimizeHorizontalLayoutGroup(HorizontalLayoutGroup layoutGroup)
    {
        var record = new OptimizationRecord(layoutGroup.gameObject, "HorizontalLayoutGroup");
        
        record.originalValues["spacing"] = layoutGroup.spacing;
        float newSpacing = Mathf.Clamp(layoutGroup.spacing * spacingMultiplier, minSpacing, maxSpacing);
        layoutGroup.spacing = newSpacing;
        record.optimizedValues["spacing"] = newSpacing;
        
        optimizationRecords.Add(record);
    }
    
    /// <summary>
    /// 优化垂直布局组
    /// </summary>
    /// <param name="layoutGroup">垂直布局组</param>
    private void OptimizeVerticalLayoutGroup(VerticalLayoutGroup layoutGroup)
    {
        var record = new OptimizationRecord(layoutGroup.gameObject, "VerticalLayoutGroup");
        
        record.originalValues["spacing"] = layoutGroup.spacing;
        float newSpacing = Mathf.Clamp(layoutGroup.spacing * spacingMultiplier, minSpacing, maxSpacing);
        layoutGroup.spacing = newSpacing;
        record.optimizedValues["spacing"] = newSpacing;
        
        optimizationRecords.Add(record);
    }
    
    /// <summary>
    /// 优化网格布局组
    /// </summary>
    /// <param name="layoutGroup">网格布局组</param>
    private void OptimizeGridLayoutGroup(GridLayoutGroup layoutGroup)
    {
        var record = new OptimizationRecord(layoutGroup.gameObject, "GridLayoutGroup");
        
        record.originalValues["spacing"] = layoutGroup.spacing;
        Vector2 newSpacing = layoutGroup.spacing * spacingMultiplier;
        newSpacing.x = Mathf.Clamp(newSpacing.x, minSpacing, maxSpacing);
        newSpacing.y = Mathf.Clamp(newSpacing.y, minSpacing, maxSpacing);
        layoutGroup.spacing = newSpacing;
        record.optimizedValues["spacing"] = newSpacing;
        
        optimizationRecords.Add(record);
    }
    
    /// <summary>
    /// 优化文本颜色
    /// </summary>
    /// <param name="originalColor">原始颜色</param>
    /// <returns>优化后的颜色</returns>
    private Color OptimizeTextColor(Color originalColor)
    {
        // 增强对比度，使文本在VR中更清晰
        Color optimizedColor = originalColor;
        
        // 如果颜色太暗，调亮
        if (originalColor.grayscale < 0.5f)
        {
            optimizedColor = Color.Lerp(originalColor, vrTextColor, contrastBoost);
        }
        
        return optimizedColor;
    }
    
    /// <summary>
    /// 优化图像颜色
    /// </summary>
    /// <param name="originalColor">原始颜色</param>
    /// <returns>优化后的颜色</returns>
    private Color OptimizeImageColor(Color originalColor)
    {
        // 增强饱和度和对比度
        Color.RGBToHSV(originalColor, out float h, out float s, out float v);
        
        // 增强饱和度
        s = Mathf.Clamp01(s + contrastBoost);
        
        // 调整亮度以提高对比度
        if (v < 0.5f)
        {
            v = Mathf.Clamp01(v + contrastBoost);
        }
        
        return Color.HSVToRGB(h, s, v);
    }
    
    /// <summary>
    /// 恢复原始布局
    /// </summary>
    public void RestoreOriginalLayout()
    {
        Debug.Log("[VRUILayoutOptimizer] 恢复原始布局");
        
        foreach (var record in optimizationRecords)
        {
            if (record.target == null) continue;
            
            RestoreElement(record);
        }
        
        optimizationRecords.Clear();
    }
    
    /// <summary>
    /// 恢复单个元素
    /// </summary>
    /// <param name="record">优化记录</param>
    private void RestoreElement(OptimizationRecord record)
    {
        switch (record.componentType)
        {
            case "Text":
                RestoreText(record);
                break;
            case "Button":
                RestoreButton(record);
                break;
            case "Slider":
                RestoreSlider(record);
                break;
            case "Image":
                RestoreImage(record);
                break;
            case "HorizontalLayoutGroup":
            case "VerticalLayoutGroup":
            case "GridLayoutGroup":
                RestoreLayoutGroup(record);
                break;
        }
    }
    
    /// <summary>
    /// 恢复文本元素
    /// </summary>
    /// <param name="record">优化记录</param>
    private void RestoreText(OptimizationRecord record)
    {
        Text text = record.target.GetComponent<Text>();
        if (text == null) return;
        
        if (record.originalValues.ContainsKey("fontSize"))
            text.fontSize = (int)record.originalValues["fontSize"];
        if (record.originalValues.ContainsKey("fontStyle"))
            text.fontStyle = (FontStyle)record.originalValues["fontStyle"];
        if (record.originalValues.ContainsKey("color"))
            text.color = (Color)record.originalValues["color"];
    }
    
    /// <summary>
    /// 恢复按钮元素
    /// </summary>
    /// <param name="record">优化记录</param>
    private void RestoreButton(OptimizationRecord record)
    {
        Button button = record.target.GetComponent<Button>();
        if (button == null) return;
        
        RectTransform rectTransform = button.GetComponent<RectTransform>();
        if (rectTransform != null && record.originalValues.ContainsKey("sizeDelta"))
        {
            rectTransform.sizeDelta = (Vector2)record.originalValues["sizeDelta"];
        }
        
        Image buttonImage = button.GetComponent<Image>();
        if (buttonImage != null && record.originalValues.ContainsKey("color"))
        {
            buttonImage.color = (Color)record.originalValues["color"];
        }
    }
    
    /// <summary>
    /// 恢复滑动条元素
    /// </summary>
    /// <param name="record">优化记录</param>
    private void RestoreSlider(OptimizationRecord record)
    {
        Slider slider = record.target.GetComponent<Slider>();
        if (slider == null) return;
        
        if (slider.handleRect != null && record.originalValues.ContainsKey("handleSize"))
        {
            slider.handleRect.sizeDelta = (Vector2)record.originalValues["handleSize"];
        }
        
        RectTransform sliderRect = slider.GetComponent<RectTransform>();
        if (sliderRect != null && record.originalValues.ContainsKey("height"))
        {
            Vector2 size = sliderRect.sizeDelta;
            size.y = (float)record.originalValues["height"];
            sliderRect.sizeDelta = size;
        }
    }
    
    /// <summary>
    /// 恢复图像元素
    /// </summary>
    /// <param name="record">优化记录</param>
    private void RestoreImage(OptimizationRecord record)
    {
        Image image = record.target.GetComponent<Image>();
        if (image != null && record.originalValues.ContainsKey("color"))
        {
            image.color = (Color)record.originalValues["color"];
        }
    }
    
    /// <summary>
    /// 恢复布局组元素
    /// </summary>
    /// <param name="record">优化记录</param>
    private void RestoreLayoutGroup(OptimizationRecord record)
    {
        if (!record.originalValues.ContainsKey("spacing")) return;
        
        switch (record.componentType)
        {
            case "HorizontalLayoutGroup":
                var hLayout = record.target.GetComponent<HorizontalLayoutGroup>();
                if (hLayout != null)
                    hLayout.spacing = (float)record.originalValues["spacing"];
                break;
            case "VerticalLayoutGroup":
                var vLayout = record.target.GetComponent<VerticalLayoutGroup>();
                if (vLayout != null)
                    vLayout.spacing = (float)record.originalValues["spacing"];
                break;
            case "GridLayoutGroup":
                var gLayout = record.target.GetComponent<GridLayoutGroup>();
                if (gLayout != null)
                    gLayout.spacing = (Vector2)record.originalValues["spacing"];
                break;
        }
    }
}
