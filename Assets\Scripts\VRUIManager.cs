using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;

// 条件编译：只在安装了XR Interaction Toolkit时编译XR相关代码
#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.UI;
#endif

/// <summary>
/// VR UI管理器
/// 
/// 专门为PICO VR环境设计的UI管理系统
/// 处理VR控制器与UI的交互，优化VR环境下的用户体验
/// </summary>
public class VRUIManager : MonoBehaviour
{
    [Header("VR UI配置")]
    [SerializeField] private Canvas vrCanvas;
    [SerializeField] private Camera vrCamera;
    [SerializeField] private bool autoFindComponents = true;
    
    [Header("UI定位设置")]
    [SerializeField] private float uiDistance = 2.0f; // UI距离用户的距离
    [SerializeField] private float uiScale = 0.01f; // UI缩放比例
    [SerializeField] private Vector3 uiOffset = Vector3.zero; // UI偏移
    [SerializeField] private bool followUserHead = false; // 是否跟随用户头部
    [SerializeField] private bool alwaysFaceUser = true; // 是否始终面向用户
    
    [Header("交互设置")]
    [SerializeField] private LayerMask uiLayerMask = -1; // UI交互层级
    [SerializeField] private bool enableHapticFeedback = true; // 启用触觉反馈
    [SerializeField] private float hapticIntensity = 0.3f; // 触觉反馈强度
    [SerializeField] private float hapticDuration = 0.1f; // 触觉反馈持续时间
    
    [Header("视觉反馈")]
    [SerializeField] private bool enableHoverEffects = true; // 启用悬停效果
    [SerializeField] private Color hoverColor = Color.yellow; // 悬停颜色
    [SerializeField] private float hoverScaleMultiplier = 1.1f; // 悬停缩放倍数
    
    // 内部状态
    private bool isInitialized = false;
    private EventSystem eventSystem;
    private GraphicRaycaster graphicRaycaster;
    
#if UNITY_XR_INTERACTION_TOOLKIT
    private TrackedDeviceGraphicRaycaster trackedRaycaster;
    private XRRayInteractor[] rayInteractors;
#endif
    
    // 交互状态
    private GameObject currentHoveredObject;
    private Vector3 originalUIPosition;
    private Quaternion originalUIRotation;
    
    void Start()
    {
        InitializeVRUI();
    }
    
    void Update()
    {
        if (isInitialized)
        {
            UpdateUIPosition();
            HandleVRInteraction();
        }
    }
    
    /// <summary>
    /// 初始化VR UI系统
    /// </summary>
    private void InitializeVRUI()
    {
        Debug.Log("[VRUIManager] 初始化VR UI系统");
        
        // 自动查找组件
        if (autoFindComponents)
        {
            FindRequiredComponents();
        }
        
        // 验证必要组件
        if (!ValidateComponents())
        {
            Debug.LogError("[VRUIManager] 缺少必要组件，无法初始化VR UI");
            return;
        }
        
        // 配置Canvas
        ConfigureCanvas();
        
        // 配置事件系统
        ConfigureEventSystem();
        
        // 设置初始位置
        SetInitialUIPosition();
        
        isInitialized = true;
        Debug.Log("[VRUIManager] VR UI系统初始化完成");
    }
    
    /// <summary>
    /// 查找必要组件
    /// </summary>
    private void FindRequiredComponents()
    {
        // 查找Canvas
        if (vrCanvas == null)
        {
            vrCanvas = GetComponent<Canvas>();
            if (vrCanvas == null)
            {
                vrCanvas = FindObjectOfType<Canvas>();
            }
        }
        
        // 查找VR相机
        if (vrCamera == null)
        {
            // 优先查找XR相机
            Camera[] cameras = FindObjectsOfType<Camera>();
            foreach (var cam in cameras)
            {
                if (cam.name.Contains("XR") || cam.name.Contains("VR") || cam.name.Contains("Main"))
                {
                    vrCamera = cam;
                    break;
                }
            }
            
            if (vrCamera == null)
            {
                vrCamera = Camera.main;
            }
        }
        
        // 查找事件系统
        eventSystem = FindObjectOfType<EventSystem>();
        
#if UNITY_XR_INTERACTION_TOOLKIT
        // 查找射线交互器
        rayInteractors = FindObjectsOfType<XRRayInteractor>();
#endif
    }
    
    /// <summary>
    /// 验证必要组件
    /// </summary>
    /// <returns>是否所有必要组件都存在</returns>
    private bool ValidateComponents()
    {
        if (vrCanvas == null)
        {
            Debug.LogError("[VRUIManager] 未找到Canvas组件");
            return false;
        }
        
        if (vrCamera == null)
        {
            Debug.LogError("[VRUIManager] 未找到VR相机");
            return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// 配置Canvas为VR模式
    /// </summary>
    private void ConfigureCanvas()
    {
        // 设置为World Space模式
        vrCanvas.renderMode = RenderMode.WorldSpace;
        vrCanvas.worldCamera = vrCamera;
        
        // 获取或添加GraphicRaycaster
        graphicRaycaster = vrCanvas.GetComponent<GraphicRaycaster>();
        if (graphicRaycaster == null)
        {
            graphicRaycaster = vrCanvas.gameObject.AddComponent<GraphicRaycaster>();
        }
        
#if UNITY_XR_INTERACTION_TOOLKIT
        // 添加TrackedDeviceGraphicRaycaster用于VR交互
        trackedRaycaster = vrCanvas.GetComponent<TrackedDeviceGraphicRaycaster>();
        if (trackedRaycaster == null)
        {
            trackedRaycaster = vrCanvas.gameObject.AddComponent<TrackedDeviceGraphicRaycaster>();
        }
#endif
        
        Debug.Log("[VRUIManager] Canvas已配置为VR模式");
    }
    
    /// <summary>
    /// 配置事件系统
    /// </summary>
    private void ConfigureEventSystem()
    {
        if (eventSystem == null)
        {
            // 创建事件系统
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystem = eventSystemGO.AddComponent<EventSystem>();
            
#if UNITY_XR_INTERACTION_TOOLKIT
            // 添加XR UI输入模块
            var xrUIInputModule = eventSystemGO.AddComponent<XRUIInputModule>();
#else
            // 添加标准输入模块
            eventSystemGO.AddComponent<StandaloneInputModule>();
#endif
        }
    }
    
    /// <summary>
    /// 设置UI初始位置
    /// </summary>
    private void SetInitialUIPosition()
    {
        if (vrCanvas != null && vrCamera != null)
        {
            PositionUI();
            
            // 保存原始位置和旋转
            originalUIPosition = vrCanvas.transform.position;
            originalUIRotation = vrCanvas.transform.rotation;
        }
    }
    
    /// <summary>
    /// 定位UI
    /// </summary>
    private void PositionUI()
    {
        Vector3 cameraPosition = vrCamera.transform.position;
        Vector3 cameraForward = vrCamera.transform.forward;
        
        // 计算UI位置
        Vector3 targetPosition = cameraPosition + cameraForward * uiDistance + uiOffset;
        vrCanvas.transform.position = targetPosition;
        
        // 设置缩放
        vrCanvas.transform.localScale = Vector3.one * uiScale;
        
        // 面向用户
        if (alwaysFaceUser)
        {
            vrCanvas.transform.LookAt(vrCamera.transform);
            vrCanvas.transform.Rotate(0, 180, 0); // 翻转让UI正面朝向用户
        }
    }
    
    /// <summary>
    /// 更新UI位置
    /// </summary>
    private void UpdateUIPosition()
    {
        if (followUserHead && vrCanvas != null && vrCamera != null)
        {
            PositionUI();
        }
        else if (alwaysFaceUser && vrCanvas != null && vrCamera != null)
        {
            // 只更新旋转，保持位置不变
            vrCanvas.transform.LookAt(vrCamera.transform);
            vrCanvas.transform.Rotate(0, 180, 0);
        }
    }
    
    /// <summary>
    /// 处理VR交互
    /// </summary>
    private void HandleVRInteraction()
    {
        // 这里可以添加额外的VR交互逻辑
        // 比如手势识别、语音控制等
    }
    
    /// <summary>
    /// 重新定位UI到用户前方
    /// </summary>
    public void RepositionUIToUser()
    {
        if (vrCanvas != null && vrCamera != null)
        {
            PositionUI();
            Debug.Log("[VRUIManager] UI已重新定位到用户前方");
        }
    }
    
    /// <summary>
    /// 重置UI到原始位置
    /// </summary>
    public void ResetUIPosition()
    {
        if (vrCanvas != null)
        {
            vrCanvas.transform.position = originalUIPosition;
            vrCanvas.transform.rotation = originalUIRotation;
            Debug.Log("[VRUIManager] UI已重置到原始位置");
        }
    }
    
    /// <summary>
    /// 设置UI跟随模式
    /// </summary>
    /// <param name="follow">是否跟随用户头部</param>
    public void SetFollowMode(bool follow)
    {
        followUserHead = follow;
        Debug.Log($"[VRUIManager] UI跟随模式: {(follow ? "启用" : "禁用")}");
    }
    
    /// <summary>
    /// 设置UI距离
    /// </summary>
    /// <param name="distance">新的距离</param>
    public void SetUIDistance(float distance)
    {
        uiDistance = Mathf.Clamp(distance, 0.5f, 10f);
        if (vrCanvas != null)
        {
            PositionUI();
        }
    }
    
    /// <summary>
    /// 设置UI缩放
    /// </summary>
    /// <param name="scale">新的缩放比例</param>
    public void SetUIScale(float scale)
    {
        uiScale = Mathf.Clamp(scale, 0.001f, 0.1f);
        if (vrCanvas != null)
        {
            vrCanvas.transform.localScale = Vector3.one * uiScale;
        }
    }
}
