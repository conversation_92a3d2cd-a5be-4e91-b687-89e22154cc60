Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16091 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/nwu/Assembly/UnityProjects/VRAssembly
-logFile
Logs/AssetImportWorker1.log
-srvPort
4081
Successfully changed project path to: D:/nwu/Assembly/UnityProjects/VRAssembly
D:/nwu/Assembly/UnityProjects/VRAssembly
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [29420] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4036751096 [EditorId] 4036751096 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [29420] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4036751096 [EditorId] 4036751096 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 50.66 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 1.99 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/Assembly/UnityProjects/VRAssembly/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56808
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.018916 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 463 ms
Refreshing native plugins compatible for Editor in 36.96 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.22 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.992 seconds
Domain Reload Profiling:
	ReloadAssembly (992ms)
		BeginReloadAssembly (117ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (760ms)
			LoadAssemblies (107ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (65ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (15ms)
			SetupLoadedEditorAssemblies (632ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (517ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (37ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (52ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015988 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 38.24 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.26 ms.
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
0x00007ff6c74fefad (Unity) StackWalker::GetCurrentCallstack
0x00007ff6c7505b39 (Unity) StackWalker::ShowCallstack
0x00007ff6c84a8df3 (Unity) GetStacktrace
0x00007ff6c8b7165d (Unity) DebugStringToFile
0x00007ff6c664ea32 (Unity) DebugLogHandler_CUSTOM_Internal_Log
0x00000241ba29e1e3 (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
0x00000241ba29debb (Mono JIT Code) UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
0x00000241ba29dc40 (Mono JIT Code) UnityEngine.Logger:Log (UnityEngine.LogType,object)
0x00000241ba29db08 (Mono JIT Code) UnityEngine.Debug:Log (object)
0x00000241ba29af43 (Mono JIT Code) [PXR_SDKBuildCheck.cs:32] Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () 
0x00000241ba05a9f5 (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c467b79 (mono-2.0-bdwgc) [object.c:563] mono_runtime_class_init_full 
0x00007ffb3c39ddd7 (mono-2.0-bdwgc) [icall.c:1292] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor 
0x00007ffb3c3cfebc (mono-2.0-bdwgc) [icall-def.h:756] ves_icall_System_Runtime_CompilerServices_RuntimeHelpers_RunClassConstructor_raw 
0x00000241ba05a09a (Mono JIT Code) (wrapper managed-to-native) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (intptr)
0x00000241ba059fab (Mono JIT Code) System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
0x00000241ba0596f3 (Mono JIT Code) UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])
0x00000241ba676298 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_void_object (object,intptr,intptr,intptr)
0x00007ffb3c53697e (mono-2.0-bdwgc) [mini-runtime.c:3445] mono_jit_runtime_invoke 
0x00007ffb3c46cdd4 (mono-2.0-bdwgc) [object.c:3068] do_runtime_invoke 
0x00007ffb3c46cf4c (mono-2.0-bdwgc) [object.c:3115] mono_runtime_invoke 
0x00007ff6c7421934 (Unity) scripting_method_invoke
0x00007ff6c7400b84 (Unity) ScriptingInvocation::Invoke
0x00007ff6c73fb6e5 (Unity) ScriptingInvocation::Invoke<void>
0x00007ff6c753f4c3 (Unity) Scripting::UnityEditor::EditorAssembliesProxy::ProcessInitializeOnLoadAttributes
0x00007ff6c73f570b (Unity) MonoManager::SetupLoadedEditorAssemblies
0x00007ff6c73eb1ed (Unity) MonoManager::EndReloadAssembly
0x00007ff6c73f2d88 (Unity) MonoManager::ReloadAssembly
0x00007ff6c835fa12 (Unity) LoadDomainAndUserAssemblies
0x00007ff6c8360144 (Unity) LoadUserAssemblies
0x00007ff6c8060c43 (Unity) Application::InitializeProject
0x00007ff6c84b20a0 (Unity) WinMain
0x00007ff6c987fbae (Unity) __scrt_common_main_seh
0x00007ffbd486259d (KERNEL32) BaseThreadInitThunk
0x00007ffbd4e8af78 (ntdll) RtlUserThreadStart

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.510 seconds
Domain Reload Profiling:
	ReloadAssembly (1510ms)
		BeginReloadAssembly (137ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (13ms)
		EndReloadAssembly (1268ms)
			LoadAssemblies (129ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (184ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (39ms)
			SetupLoadedEditorAssemblies (917ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (39ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (733ms)
				ProcessInitializeOnLoadMethodAttributes (23ms)
				AfterProcessingInitializeOnLoad (35ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 13 plugins.
Preloading 2 native plugins for Editor in 0.18 ms.
Unloading 4794 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (122.8 KB). Loaded Objects now: 5248.
Memory consumption went from 195.7 MB to 195.6 MB.
Total: 2.643300 ms (FindLiveObjects: 0.210100 ms CreateObjectMapping: 0.065100 ms MarkObjects: 2.287400 ms  DeleteObjects: 0.079900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 401651.686764 seconds.
  path: Assets/Scripts/VRUIManager.cs
  artifactKey: Guid(fe417f333ddbeb449b05c3468bb64ed4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/VRUIManager.cs using Guid(fe417f333ddbeb449b05c3468bb64ed4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e7315a6bebb52903ee589a8a23d164b7') in 0.015651 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 144.792156 seconds.
  path: Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/Prefabs/Interactors/Direct Interactor.prefab
  artifactKey: Guid(2fd3e07afe5b461490fb8e314976b1b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/Prefabs/Interactors/Direct Interactor.prefab using Guid(2fd3e07afe5b461490fb8e314976b1b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '17187571ca94328e1649e16bbeaef29a') in 0.030782 seconds 
Number of asset objects unloaded after import = 6
========================================================================
Received Import Request.
  Time since last request: 0.017947 seconds.
  path: Assets/Samples/XR Interaction Toolkit/2.5.2/XR Device Simulator/Hand Expression Captures/Fist Expression Capture.asset
  artifactKey: Guid(2a7c8ca0feac7cc44a5c225164ef311d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Samples/XR Interaction Toolkit/2.5.2/XR Device Simulator/Hand Expression Captures/Fist Expression Capture.asset using Guid(2a7c8ca0feac7cc44a5c225164ef311d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eef6c6b73a5e4b56811e9215fd969da1') in 0.005080 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.035302 seconds.
  path: Assets/Samples/XR Interaction Toolkit/2.5.2/XR Device Simulator/Hand Expression Captures/Open Expression Capture.asset
  artifactKey: Guid(05293ab353dc8a747a36ed129311686d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Samples/XR Interaction Toolkit/2.5.2/XR Device Simulator/Hand Expression Captures/Open Expression Capture.asset using Guid(05293ab353dc8a747a36ed129311686d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f215320c376e652800c8b9dcb369bb38') in 0.003281 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.085745 seconds.
  path: Assets/Samples/XR Interaction Toolkit/2.5.2/XR Device Simulator/Hand Expression Captures/Grab Expression Capture.asset
  artifactKey: Guid(3861c298d39c60c44b16920421444875) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Samples/XR Interaction Toolkit/2.5.2/XR Device Simulator/Hand Expression Captures/Grab Expression Capture.asset using Guid(3861c298d39c60c44b16920421444875) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7a5dece327b1c276547604dffe5b5961') in 0.003064 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.108511 seconds.
  path: Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/DemoSceneAssets/Prefabs/Teleport/Teleport Area.prefab
  artifactKey: Guid(beb4e2871579447497fe41dfb108e2cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Samples/XR Interaction Toolkit/2.5.2/Starter Assets/DemoSceneAssets/Prefabs/Teleport/Teleport Area.prefab using Guid(beb4e2871579447497fe41dfb108e2cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0728fc620aae7d33c713cfedd22c3480') in 0.153963 seconds 
Number of asset objects unloaded after import = 61
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0