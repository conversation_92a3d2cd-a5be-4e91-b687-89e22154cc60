using UnityEngine;
using UnityEngine.UI;
using System.Collections;

/// <summary>
/// VR装配UI集成器
/// 
/// 将VR UI系统与现有的装配控制系统集成
/// 提供完整的VR装配用户界面解决方案
/// </summary>
public class VRAssemblyUIIntegrator : MonoBehaviour
{
    [Header("核心组件引用")]
    [SerializeField] private Neo4jAssemblyUI assemblyUI;
    [SerializeField] private VRUIManager vrUIManager;
    [SerializeField] private VRUIInteractor vrUIInteractor;
    [SerializeField] private VRUILayoutOptimizer vrUIOptimizer;
    [SerializeField] private VRInputManager vrInputManager;
    
    [Header("VR UI设置")]
    [SerializeField] private bool enableVRMode = true;
    [SerializeField] private bool autoInitialize = true;
    [SerializeField] private bool enableDebugMode = false;
    
    [Header("UI面板配置")]
    [SerializeField] private GameObject mainUIPanel;
    [SerializeField] private GameObject controlPanel;
    [SerializeField] private GameObject statusPanel;
    [SerializeField] private GameObject debugPanel;
    
    [Header("VR特定UI元素")]
    [SerializeField] private Button vrRepositionButton;
    [SerializeField] private Button vrResetViewButton;
    [SerializeField] private Toggle vrFollowModeToggle;
    [SerializeField] private Slider vrUIDistanceSlider;
    [SerializeField] private Slider vrUIScaleSlider;
    
    [Header("状态显示")]
    [SerializeField] private Text vrStatusText;
    [SerializeField] private Text vrModeText;
    [SerializeField] private Text vrControllerStatusText;
    
    // 内部状态
    private bool isVRModeActive = false;
    private bool isInitialized = false;
    private Coroutine statusUpdateCoroutine;
    
    void Start()
    {
        if (autoInitialize)
        {
            InitializeVRUI();
        }
    }
    
    void OnDestroy()
    {
        if (statusUpdateCoroutine != null)
        {
            StopCoroutine(statusUpdateCoroutine);
        }
    }
    
    /// <summary>
    /// 初始化VR UI系统
    /// </summary>
    public void InitializeVRUI()
    {
        Debug.Log("[VRAssemblyUIIntegrator] 初始化VR装配UI系统");
        
        // 查找必要组件
        FindRequiredComponents();
        
        // 验证组件
        if (!ValidateComponents())
        {
            Debug.LogError("[VRAssemblyUIIntegrator] 组件验证失败，无法初始化");
            return;
        }
        
        // 配置VR模式
        ConfigureVRMode();
        
        // 设置事件监听
        SetupEventListeners();
        
        // 配置UI面板
        ConfigureUIPanels();
        
        // 启动状态更新
        if (enableDebugMode)
        {
            statusUpdateCoroutine = StartCoroutine(UpdateStatusCoroutine());
        }
        
        isInitialized = true;
        Debug.Log("[VRAssemblyUIIntegrator] VR装配UI系统初始化完成");
    }
    
    /// <summary>
    /// 查找必要组件
    /// </summary>
    private void FindRequiredComponents()
    {
        // 查找装配UI组件
        if (assemblyUI == null)
        {
            assemblyUI = FindObjectOfType<Neo4jAssemblyUI>();
        }
        
        // 查找VR UI管理器
        if (vrUIManager == null)
        {
            vrUIManager = FindObjectOfType<VRUIManager>();
            if (vrUIManager == null)
            {
                // 自动添加VR UI管理器
                GameObject vrUIManagerGO = new GameObject("VRUIManager");
                vrUIManager = vrUIManagerGO.AddComponent<VRUIManager>();
            }
        }
        
        // 查找VR UI交互器
        if (vrUIInteractor == null)
        {
            vrUIInteractor = FindObjectOfType<VRUIInteractor>();
            if (vrUIInteractor == null)
            {
                // 自动添加到VR UI管理器对象上
                vrUIInteractor = vrUIManager.gameObject.AddComponent<VRUIInteractor>();
            }
        }
        
        // 查找VR UI优化器
        if (vrUIOptimizer == null)
        {
            vrUIOptimizer = GetComponent<VRUILayoutOptimizer>();
            if (vrUIOptimizer == null)
            {
                vrUIOptimizer = gameObject.AddComponent<VRUILayoutOptimizer>();
            }
        }
        
        // 查找VR输入管理器
        if (vrInputManager == null)
        {
            vrInputManager = FindObjectOfType<VRInputManager>();
        }
    }
    
    /// <summary>
    /// 验证必要组件
    /// </summary>
    /// <returns>是否所有必要组件都存在</returns>
    private bool ValidateComponents()
    {
        if (assemblyUI == null)
        {
            Debug.LogError("[VRAssemblyUIIntegrator] 未找到Neo4jAssemblyUI组件");
            return false;
        }
        
        if (vrUIManager == null)
        {
            Debug.LogError("[VRAssemblyUIIntegrator] 未找到VRUIManager组件");
            return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// 配置VR模式
    /// </summary>
    private void ConfigureVRMode()
    {
        isVRModeActive = enableVRMode;
        
        if (isVRModeActive)
        {
            // 启用VR UI优化
            if (vrUIOptimizer != null)
            {
                vrUIOptimizer.OptimizeAllUIElements();
            }
            
            // 更新状态显示
            UpdateVRModeDisplay();
        }
        
        Debug.Log($"[VRAssemblyUIIntegrator] VR模式: {(isVRModeActive ? "启用" : "禁用")}");
    }
    
    /// <summary>
    /// 设置事件监听
    /// </summary>
    private void SetupEventListeners()
    {
        // VR输入管理器事件
        if (vrInputManager != null)
        {
            vrInputManager.OnPartSelected += OnVRPartSelected;
            vrInputManager.OnNextStepRequested += OnVRNextStepRequested;
            vrInputManager.OnResetRequested += OnVRResetRequested;
            vrInputManager.OnReplayRequested += OnVRReplayRequested;
        }
        
        // VR UI按钮事件
        if (vrRepositionButton != null)
        {
            vrRepositionButton.onClick.AddListener(OnVRRepositionButtonClicked);
        }
        
        if (vrResetViewButton != null)
        {
            vrResetViewButton.onClick.AddListener(OnVRResetViewButtonClicked);
        }
        
        if (vrFollowModeToggle != null)
        {
            vrFollowModeToggle.onValueChanged.AddListener(OnVRFollowModeToggleChanged);
        }
        
        if (vrUIDistanceSlider != null)
        {
            vrUIDistanceSlider.onValueChanged.AddListener(OnVRUIDistanceSliderChanged);
        }
        
        if (vrUIScaleSlider != null)
        {
            vrUIScaleSlider.onValueChanged.AddListener(OnVRUIScaleSliderChanged);
        }
    }
    
    /// <summary>
    /// 配置UI面板
    /// </summary>
    private void ConfigureUIPanels()
    {
        // 根据VR模式显示/隐藏相应面板
        if (isVRModeActive)
        {
            // 显示VR特定的控制面板
            if (controlPanel != null)
            {
                controlPanel.SetActive(true);
            }
            
            // 显示调试面板（如果启用调试模式）
            if (debugPanel != null)
            {
                debugPanel.SetActive(enableDebugMode);
            }
        }
        else
        {
            // 隐藏VR特定面板
            if (controlPanel != null)
            {
                controlPanel.SetActive(false);
            }
            
            if (debugPanel != null)
            {
                debugPanel.SetActive(false);
            }
        }
    }
    
    /// <summary>
    /// 更新VR模式显示
    /// </summary>
    private void UpdateVRModeDisplay()
    {
        if (vrModeText != null)
        {
            vrModeText.text = $"VR模式: {(isVRModeActive ? "启用" : "禁用")}";
        }
    }
    
    /// <summary>
    /// 状态更新协程
    /// </summary>
    private IEnumerator UpdateStatusCoroutine()
    {
        while (true)
        {
            UpdateStatusDisplay();
            yield return new WaitForSeconds(0.5f);
        }
    }
    
    /// <summary>
    /// 更新状态显示
    /// </summary>
    private void UpdateStatusDisplay()
    {
        if (!enableDebugMode) return;
        
        // 更新VR状态
        if (vrStatusText != null)
        {
            string status = "VR系统状态:\n";
            status += $"VR模式: {(isVRModeActive ? "启用" : "禁用")}\n";
            status += $"UI管理器: {(vrUIManager != null ? "正常" : "缺失")}\n";
            status += $"UI交互器: {(vrUIInteractor != null ? "正常" : "缺失")}\n";
            status += $"输入管理器: {(vrInputManager != null ? "正常" : "缺失")}";
            vrStatusText.text = status;
        }
        
        // 更新控制器状态
        if (vrControllerStatusText != null && vrInputManager != null)
        {
            // 这里可以添加控制器状态检测逻辑
            vrControllerStatusText.text = "控制器状态: 正常";
        }
    }
    
    #region VR事件处理
    
    /// <summary>
    /// VR零件选择事件处理
    /// </summary>
    /// <param name="part">选中的零件</param>
    private void OnVRPartSelected(AssemblyPart part)
    {
        if (assemblyUI != null)
        {
            assemblyUI.OnPartSelected(part.PartName);
        }
        
        Debug.Log($"[VRAssemblyUIIntegrator] VR选择零件: {part.PartName}");
    }
    
    /// <summary>
    /// VR下一步请求事件处理
    /// </summary>
    private void OnVRNextStepRequested()
    {
        if (assemblyUI != null)
        {
            // 模拟点击下一步按钮
            var nextStepButton = assemblyUI.GetComponentInChildren<Button>();
            if (nextStepButton != null && nextStepButton.name.Contains("NextStep"))
            {
                nextStepButton.onClick.Invoke();
            }
        }
        
        Debug.Log("[VRAssemblyUIIntegrator] VR请求下一步");
    }
    
    /// <summary>
    /// VR重置请求事件处理
    /// </summary>
    private void OnVRResetRequested()
    {
        if (assemblyUI != null)
        {
            // 模拟点击重置按钮
            var resetButton = assemblyUI.GetComponentInChildren<Button>();
            if (resetButton != null && resetButton.name.Contains("Reset"))
            {
                resetButton.onClick.Invoke();
            }
        }
        
        Debug.Log("[VRAssemblyUIIntegrator] VR请求重置");
    }
    
    /// <summary>
    /// VR重播请求事件处理
    /// </summary>
    private void OnVRReplayRequested()
    {
        if (assemblyUI != null)
        {
            // 模拟点击重播按钮
            var replayButton = assemblyUI.GetComponentInChildren<Button>();
            if (replayButton != null && replayButton.name.Contains("Replay"))
            {
                replayButton.onClick.Invoke();
            }
        }
        
        Debug.Log("[VRAssemblyUIIntegrator] VR请求重播");
    }
    
    /// <summary>
    /// VR重新定位按钮点击事件
    /// </summary>
    private void OnVRRepositionButtonClicked()
    {
        if (vrUIManager != null)
        {
            vrUIManager.RepositionUIToUser();
        }
        
        Debug.Log("[VRAssemblyUIIntegrator] VR UI重新定位");
    }
    
    /// <summary>
    /// VR重置视图按钮点击事件
    /// </summary>
    private void OnVRResetViewButtonClicked()
    {
        if (vrUIManager != null)
        {
            vrUIManager.ResetUIPosition();
        }
        
        Debug.Log("[VRAssemblyUIIntegrator] VR视图重置");
    }
    
    /// <summary>
    /// VR跟随模式切换事件
    /// </summary>
    /// <param name="isOn">是否启用跟随模式</param>
    private void OnVRFollowModeToggleChanged(bool isOn)
    {
        if (vrUIManager != null)
        {
            vrUIManager.SetFollowMode(isOn);
        }
        
        Debug.Log($"[VRAssemblyUIIntegrator] VR跟随模式: {(isOn ? "启用" : "禁用")}");
    }
    
    /// <summary>
    /// VR UI距离滑动条变化事件
    /// </summary>
    /// <param name="value">新的距离值</param>
    private void OnVRUIDistanceSliderChanged(float value)
    {
        if (vrUIManager != null)
        {
            vrUIManager.SetUIDistance(value);
        }
        
        Debug.Log($"[VRAssemblyUIIntegrator] VR UI距离: {value:F1}");
    }
    
    /// <summary>
    /// VR UI缩放滑动条变化事件
    /// </summary>
    /// <param name="value">新的缩放值</param>
    private void OnVRUIScaleSliderChanged(float value)
    {
        if (vrUIManager != null)
        {
            vrUIManager.SetUIScale(value);
        }
        
        Debug.Log($"[VRAssemblyUIIntegrator] VR UI缩放: {value:F3}");
    }
    
    #endregion
    
    #region 公共接口
    
    /// <summary>
    /// 切换VR模式
    /// </summary>
    /// <param name="enable">是否启用VR模式</param>
    public void SetVRMode(bool enable)
    {
        enableVRMode = enable;
        if (isInitialized)
        {
            ConfigureVRMode();
            ConfigureUIPanels();
        }
    }
    
    /// <summary>
    /// 切换调试模式
    /// </summary>
    /// <param name="enable">是否启用调试模式</param>
    public void SetDebugMode(bool enable)
    {
        enableDebugMode = enable;
        
        if (debugPanel != null)
        {
            debugPanel.SetActive(enable && isVRModeActive);
        }
        
        if (enable && statusUpdateCoroutine == null)
        {
            statusUpdateCoroutine = StartCoroutine(UpdateStatusCoroutine());
        }
        else if (!enable && statusUpdateCoroutine != null)
        {
            StopCoroutine(statusUpdateCoroutine);
            statusUpdateCoroutine = null;
        }
    }
    
    /// <summary>
    /// 获取VR模式状态
    /// </summary>
    /// <returns>是否启用VR模式</returns>
    public bool IsVRModeActive()
    {
        return isVRModeActive;
    }
    
    /// <summary>
    /// 获取初始化状态
    /// </summary>
    /// <returns>是否已初始化</returns>
    public bool IsInitialized()
    {
        return isInitialized;
    }
    
    #endregion
}
