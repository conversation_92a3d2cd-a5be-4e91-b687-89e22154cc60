using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;
using System.Collections.Generic;

// 条件编译：只在安装了XR Interaction Toolkit时编译XR相关代码
#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR;
#endif

/// <summary>
/// VR UI交互器
/// 
/// 处理VR控制器与UI元素的具体交互逻辑
/// 包括按钮点击、滑动条操作、悬停效果等
/// </summary>
public class VRUIInteractor : MonoBehaviour
{
    [Header("交互设置")]
    [SerializeField] private bool enableHoverEffects = true;
    [SerializeField] private bool enableClickFeedback = true;
    [SerializeField] private bool enableHapticFeedback = true;
    
    [Header("视觉反馈")]
    [SerializeField] private Color hoverColor = new Color(1f, 1f, 0f, 0.3f);
    [SerializeField] private Color clickColor = new Color(0f, 1f, 0f, 0.5f);
    [SerializeField] private float hoverScaleMultiplier = 1.05f;
    [SerializeField] private float clickScaleMultiplier = 0.95f;
    
    [Header("触觉反馈")]
    [SerializeField] private float hoverHapticIntensity = 0.1f;
    [SerializeField] private float clickHapticIntensity = 0.3f;
    [SerializeField] private float hapticDuration = 0.1f;
    
    [Header("音效反馈")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip hoverSound;
    [SerializeField] private AudioClip clickSound;
    [SerializeField] private float soundVolume = 0.5f;
    
    // 交互状态
    private Dictionary<GameObject, UIElementState> uiElementStates = new Dictionary<GameObject, UIElementState>();
    private GameObject currentHoveredElement;
    private GameObject currentPressedElement;
    
#if UNITY_XR_INTERACTION_TOOLKIT
    private XRRayInteractor[] rayInteractors;
    private XRController[] controllers;
#endif
    
    /// <summary>
    /// UI元素状态
    /// </summary>
    private class UIElementState
    {
        public Color originalColor;
        public Vector3 originalScale;
        public bool isHovered;
        public bool isPressed;
        public Graphic graphic;
        public Transform transform;
        
        public UIElementState(GameObject uiElement)
        {
            transform = uiElement.transform;
            originalScale = transform.localScale;
            
            graphic = uiElement.GetComponent<Graphic>();
            if (graphic != null)
            {
                originalColor = graphic.color;
            }
        }
    }
    
    void Start()
    {
        InitializeInteractor();
    }
    
    void Update()
    {
        HandleVRUIInteraction();
    }
    
    /// <summary>
    /// 初始化交互器
    /// </summary>
    private void InitializeInteractor()
    {
        // 查找音频源
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.volume = soundVolume;
            }
        }
        
#if UNITY_XR_INTERACTION_TOOLKIT
        // 查找VR控制器
        rayInteractors = FindObjectsOfType<XRRayInteractor>();
        controllers = FindObjectsOfType<XRController>();
#endif
        
        Debug.Log("[VRUIInteractor] 交互器初始化完成");
    }
    
    /// <summary>
    /// 处理VR UI交互
    /// </summary>
    private void HandleVRUIInteraction()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        foreach (var rayInteractor in rayInteractors)
        {
            if (rayInteractor == null) continue;
            
            // 检查射线是否击中UI元素
            if (rayInteractor.TryGetCurrentUIRaycastResult(out RaycastResult raycastResult))
            {
                HandleUIRaycastHit(raycastResult, rayInteractor);
            }
            else
            {
                // 没有击中UI，清除悬停状态
                ClearHoverState();
            }
            
            // 处理控制器输入
            HandleControllerInput(rayInteractor);
        }
#endif
    }
    
    /// <summary>
    /// 处理UI射线击中
    /// </summary>
    /// <param name="raycastResult">射线检测结果</param>
    /// <param name="rayInteractor">射线交互器</param>
    private void HandleUIRaycastHit(RaycastResult raycastResult, object rayInteractor)
    {
        GameObject hitObject = raycastResult.gameObject;
        
        if (hitObject != currentHoveredElement)
        {
            // 清除之前的悬停状态
            ClearHoverState();
            
            // 设置新的悬停状态
            SetHoverState(hitObject);
            currentHoveredElement = hitObject;
        }
    }
    
    /// <summary>
    /// 处理控制器输入
    /// </summary>
    /// <param name="rayInteractor">射线交互器</param>
    private void HandleControllerInput(object rayInteractor)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        XRRayInteractor xrRayInteractor = rayInteractor as XRRayInteractor;
        if (xrRayInteractor == null) return;
        
        // 获取关联的控制器
        XRController controller = xrRayInteractor.GetComponent<XRController>();
        if (controller == null) return;
        
        // 检查触发器按下
        bool triggerPressed = false;
        controller.inputDevice.TryGetFeatureValue(CommonUsages.triggerButton, out triggerPressed);
        
        if (triggerPressed && currentHoveredElement != null)
        {
            HandleUIClick(currentHoveredElement, controller);
        }
        else if (currentPressedElement != null)
        {
            // 释放按下状态
            ClearPressState();
        }
#endif
    }
    
    /// <summary>
    /// 设置悬停状态
    /// </summary>
    /// <param name="uiElement">UI元素</param>
    private void SetHoverState(GameObject uiElement)
    {
        if (!enableHoverEffects) return;
        
        UIElementState state = GetOrCreateElementState(uiElement);
        if (state.isHovered) return;
        
        state.isHovered = true;
        
        // 应用悬停效果
        if (state.graphic != null)
        {
            state.graphic.color = Color.Lerp(state.originalColor, hoverColor, 0.5f);
        }
        
        state.transform.localScale = state.originalScale * hoverScaleMultiplier;
        
        // 播放悬停音效
        PlaySound(hoverSound);
        
        // 触觉反馈
        TriggerHapticFeedback(hoverHapticIntensity);
        
        Debug.Log($"[VRUIInteractor] 悬停: {uiElement.name}");
    }
    
    /// <summary>
    /// 清除悬停状态
    /// </summary>
    private void ClearHoverState()
    {
        if (currentHoveredElement != null && uiElementStates.ContainsKey(currentHoveredElement))
        {
            UIElementState state = uiElementStates[currentHoveredElement];
            state.isHovered = false;
            
            // 恢复原始外观
            if (state.graphic != null)
            {
                state.graphic.color = state.originalColor;
            }
            state.transform.localScale = state.originalScale;
            
            currentHoveredElement = null;
        }
    }
    
    /// <summary>
    /// 处理UI点击
    /// </summary>
    /// <param name="uiElement">被点击的UI元素</param>
    /// <param name="controller">控制器</param>
    private void HandleUIClick(GameObject uiElement, object controller)
    {
        if (currentPressedElement == uiElement) return;
        
        currentPressedElement = uiElement;
        UIElementState state = GetOrCreateElementState(uiElement);
        state.isPressed = true;
        
        // 应用点击效果
        if (enableClickFeedback)
        {
            if (state.graphic != null)
            {
                state.graphic.color = Color.Lerp(state.originalColor, clickColor, 0.7f);
            }
            state.transform.localScale = state.originalScale * clickScaleMultiplier;
        }
        
        // 播放点击音效
        PlaySound(clickSound);
        
        // 触觉反馈
#if UNITY_XR_INTERACTION_TOOLKIT
        XRController xrController = controller as XRController;
        if (xrController != null)
        {
            TriggerHapticFeedback(clickHapticIntensity, xrController);
        }
#endif
        
        // 执行UI元素的点击逻辑
        ExecuteUIClick(uiElement);
        
        Debug.Log($"[VRUIInteractor] 点击: {uiElement.name}");
    }
    
    /// <summary>
    /// 清除按下状态
    /// </summary>
    private void ClearPressState()
    {
        if (currentPressedElement != null && uiElementStates.ContainsKey(currentPressedElement))
        {
            UIElementState state = uiElementStates[currentPressedElement];
            state.isPressed = false;
            
            // 恢复悬停状态或原始状态
            if (state.isHovered)
            {
                if (state.graphic != null)
                {
                    state.graphic.color = Color.Lerp(state.originalColor, hoverColor, 0.5f);
                }
                state.transform.localScale = state.originalScale * hoverScaleMultiplier;
            }
            else
            {
                if (state.graphic != null)
                {
                    state.graphic.color = state.originalColor;
                }
                state.transform.localScale = state.originalScale;
            }
            
            currentPressedElement = null;
        }
    }
    
    /// <summary>
    /// 执行UI点击逻辑
    /// </summary>
    /// <param name="uiElement">UI元素</param>
    private void ExecuteUIClick(GameObject uiElement)
    {
        // 处理按钮点击
        Button button = uiElement.GetComponent<Button>();
        if (button != null && button.interactable)
        {
            button.onClick.Invoke();
            return;
        }
        
        // 处理Toggle切换
        Toggle toggle = uiElement.GetComponent<Toggle>();
        if (toggle != null && toggle.interactable)
        {
            toggle.isOn = !toggle.isOn;
            return;
        }
        
        // 处理滑动条（需要特殊处理）
        Slider slider = uiElement.GetComponent<Slider>();
        if (slider != null && slider.interactable)
        {
            // 滑动条的交互需要更复杂的逻辑，这里简化处理
            // 实际应用中可能需要根据点击位置调整滑动条值
        }
    }
    
    /// <summary>
    /// 获取或创建UI元素状态
    /// </summary>
    /// <param name="uiElement">UI元素</param>
    /// <returns>UI元素状态</returns>
    private UIElementState GetOrCreateElementState(GameObject uiElement)
    {
        if (!uiElementStates.ContainsKey(uiElement))
        {
            uiElementStates[uiElement] = new UIElementState(uiElement);
        }
        return uiElementStates[uiElement];
    }
    
    /// <summary>
    /// 播放音效
    /// </summary>
    /// <param name="clip">音频剪辑</param>
    private void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip, soundVolume);
        }
    }
    
    /// <summary>
    /// 触发触觉反馈
    /// </summary>
    /// <param name="intensity">强度</param>
    /// <param name="controller">指定的控制器</param>
    private void TriggerHapticFeedback(float intensity, object controller = null)
    {
        if (!enableHapticFeedback) return;
        
#if UNITY_XR_INTERACTION_TOOLKIT
        if (controller != null)
        {
            XRController xrController = controller as XRController;
            if (xrController != null)
            {
                xrController.SendHapticImpulse(intensity, hapticDuration);
            }
        }
        else
        {
            // 对所有控制器触发反馈
            foreach (var ctrl in controllers)
            {
                if (ctrl != null)
                {
                    ctrl.SendHapticImpulse(intensity, hapticDuration);
                }
            }
        }
#endif
    }
    
    /// <summary>
    /// 清理资源
    /// </summary>
    void OnDestroy()
    {
        uiElementStates.Clear();
    }
}
